export interface FoodEntry {
  id: string;
  name: string;
  calories: number;
  meal: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export interface ExerciseEntry {
  id: string;
  name: string;
  duration: number; // in minutes
  calories: number;
}

export interface DailyLog {
  food: FoodEntry[];
  exercise: ExerciseEntry[];
}

export interface FoodAnalysis {
    foodName: string;
    calories: number;
}

export interface UserProfile {
  age: number | null;
  sex: 'male' | 'female' | null;
  weight: number | null;
  height: number | null;
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active';
}

export interface AppState {
  dailyGoal: number;
  logs: Record<string, DailyLog>;
  customFoods: Omit<FoodEntry, 'id' | 'meal'>[];
  apiKey: string | null;
  aiModel: string;
  language: 'en' | 'zh-TW';
  userProfile: UserProfile;
  chatHistory: ChatMessage[];
}

export type ActiveView = 'dashboard' | 'log' | 'ai' | 'settings';

export interface ChatMessage {
    role: 'user' | 'model';
    text: string;
}
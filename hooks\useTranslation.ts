import { useState, useEffect, useCallback } from 'react';

const translations: Record<string, any> = {};

export const useTranslation = (language: 'en' | 'zh-TW') => {
    const [isLoaded, setIsLoaded] = useState(false);
    const locale = language === 'en' ? 'en-US' : 'zh-TW';

    useEffect(() => {
        const loadTranslations = async () => {
            setIsLoaded(false);
            try {
                if (!translations[language]) {
                    const response = await fetch(`/locales/${language}.json`);
                    if (!response.ok) {
                        throw new Error(`Failed to load ${language}.json`);
                    }
                    translations[language] = await response.json();
                }
            } catch (error) {
                console.error("Failed to load translations:", error);
                // Fallback to English if loading fails
                if (!translations['en']) {
                     try {
                        const response = await fetch(`/locales/en.json`);
                        translations['en'] = await response.json();
                     } catch(e) {
                         console.error("Failed to load fallback English translations", e);
                     }
                }
            } finally {
                setIsLoaded(true);
            }
        };

        loadTranslations();
    }, [language]);

    const t = useCallback((key: string, options?: Record<string, string | number>): string => {
        if (!isLoaded) return key; // Return key if not loaded yet to avoid blank UI
        
        const langData = translations[language] || translations['en'] || {};
        let text = key.split('.').reduce((obj, k) => obj && obj[k], langData);

        if (typeof text !== 'string') {
            console.warn(`Translation key not found: ${key} for language ${language}`);
            return key;
        }

        if (options) {
            Object.keys(options).forEach(k => {
                text = text.replace(new RegExp(`{{${k}}}`, 'g'), String(options[k]));
            });
        }
        
        return text;
    }, [language, isLoaded]);

    return { t, isLoaded, currentLanguage: language, locale };
};
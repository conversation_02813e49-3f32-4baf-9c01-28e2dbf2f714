{"appTitle": "Fitness Calorie Tracker", "loading": "Loading...", "nav.dashboard": "Dashboard", "nav.log": "Log", "nav.ai_assistant": "AI Assistant", "nav.settings": "Settings", "dashboard.title": "Dashboard", "dashboard.intake": "Intake", "dashboard.burned": "Burned", "dashboard.net": "Net", "dashboard.goal_progress": "Daily Goal Progress", "dashboard.consumed": "Consumed", "dashboard.remaining": "Remaining", "dashboard.of": "/", "dashboard.kcal": "kcal", "dashboard.net_calories_7_days": "Net Calories (Last 7 Days)", "dashboard.net_calories": "Net Calories", "log.food": "Food", "log.exercise": "Exercise", "log.log_food": "Log Food", "log.food_name": "Food Name", "log.calories": "Calories", "log.meal.breakfast": "Breakfast", "log.meal.lunch": "Lunch", "log.meal.dinner": "Dinner", "log.meal.snack": "Snack", "log.add_food": "Add Food", "log.scan_food": "<PERSON><PERSON>", "log.upload_image": "Upload", "log.log_exercise": "Log Exercise", "log.exercise_name": "Exercise Name", "log.duration_mins": "Duration (minutes)", "log.duration_unit": "min", "log.calories_burned": "Calories Burned", "log.add_exercise": "Add Exercise", "log.todays_log": "Today's Log", "log.no_food": "No food logged yet.", "log.no_exercise": "No exercise logged yet.", "log.date_log": "'s Log", "ai.title": "AI Assistant", "ai.not_configured": "AI Assistant Not Configured", "ai.not_configured_desc": "Please enter your Google Gemini API Key in the Settings page to enable this feature.", "ai.go_to_settings": "Go to Settings", "ai.welcome": "Hello! How can I help you?", "ai.example_prompt": "e.g., \"Give me some low-calorie lunch ideas.\"", "ai.ask_placeholder": "Ask AI for advice...", "settings.title": "Settings", "settings.ai_settings": "AI Settings", "settings.ai_settings_desc": "Configure your AI assistant. The API key is stored securely on your device.", "settings.api_key": "Google Gemini API Key", "settings.api_key_placeholder": "Paste your API key here", "settings.api_key_required_error": "API Key is not set. Please add it in Settings.", "settings.ai_model": "AI Model", "settings.model.flash": "Gemini 2.5 Flash (Recommended)", "settings.model.pro": "Gemini 1.5 Pro", "settings.model.gemini-pro": "Gemini Pro", "settings.language": "Language", "settings.language.en": "English", "settings.language.zh-TW": "繁體中文 (Traditional Chinese)", "settings.save": "Save All Settings", "settings.saved": "Settings saved!", "settings.user_profile.title": "User Profile", "settings.user_profile.desc": "Provide your details to calculate a more accurate daily calorie goal (TDEE).", "settings.user_profile.age": "Age", "settings.user_profile.sex": "Sex", "settings.user_profile.select_sex": "Select Sex", "settings.user_profile.male": "Male", "settings.user_profile.female": "Female", "settings.user_profile.weight": "Weight (kg)", "settings.user_profile.height": "Height (cm)", "settings.user_profile.activity_level": "Activity Level", "settings.user_profile.activity.sedentary": "Sedentary (little or no exercise)", "settings.user_profile.activity.light": "Lightly active (light exercise/sports 1-3 days/week)", "settings.user_profile.activity.moderate": "Moderately active (moderate exercise/sports 3-5 days/week)", "settings.user_profile.activity.active": "Active (hard exercise/sports 6-7 days a week)", "settings.user_profile.activity.very_active": "Very active (very hard exercise & physical job)", "settings.user_profile.calculate_goal": "Calculate & Set Goal", "settings.user_profile.validation_error": "Please fill in all profile fields to calculate your goal.", "settings.user_profile.tdee_result_1": "Your estimated daily calorie need is", "settings.user_profile.tdee_result_2": "Your daily goal has been updated.", "settings.data_management.title": "Data Management", "settings.data_management.desc": "Export your data for backup, or import it on a new device.", "settings.data_management.export": "Export Data", "settings.data_management.import": "Import Data", "settings.data_management.import_confirm": "Are you sure you want to import data? This will overwrite all current data.", "settings.data_management.import_success": "Data imported successfully! The app will now reload.", "settings.data_management.import_error": "Failed to import data. The file may be corrupt or in the wrong format.", "camera.modal_title": "Scan Food with AI", "camera.scanning": "Scanning...", "camera.scan_error": "Could not analyze image. Please try again.", "camera.unidentified": "Food not identified. Please enter manually.", "camera.permission_error": "Camera permission is required. Please enable it in your browser settings.", "general.today": "Today"}
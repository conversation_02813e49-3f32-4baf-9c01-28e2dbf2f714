import { useState, useEffect, useCallback } from 'react';
import { AppState, DailyLog, FoodEntry, ExerciseEntry, UserProfile, ChatMessage } from '../types';

const getInitialLanguage = (): 'en' | 'zh-TW' => {
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.includes('zh-tw') || browserLang.includes('zh-hk')) {
    return 'zh-TW';
  }
  return 'en';
};

const getDateString = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

const initialAppState: AppState = {
  dailyGoal: 2000,
  logs: {},
  customFoods: [],
  apiKey: null,
  aiModel: 'gemini-2.5-flash',
  language: getInitialLanguage(),
  userProfile: {
    age: null,
    sex: null,
    weight: null,
    height: null,
    activityLevel: 'moderate'
  },
  chatHistory: []
};

export const useAppState = () => {
  const [appState, setAppState] = useState<AppState>(initialAppState);
  const [isInitialized, setIsInitialized] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());

  const dateString = getDateString(selectedDate);

  useEffect(() => {
    try {
      const storedState = localStorage.getItem('calorieTrackerState');
      if (storedState) {
        const parsedState = JSON.parse(storedState);
        const language = parsedState.language || getInitialLanguage();
        const userProfile = parsedState.userProfile || initialAppState.userProfile;
        const chatHistory = parsedState.chatHistory || [];
        setAppState({ ...initialAppState, ...parsedState, language, userProfile, chatHistory });
      } else {
        setAppState(initialAppState);
      }
    } catch (error) {
      console.error("Could not load state from localStorage", error);
      setAppState(initialAppState);
    }
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (isInitialized) {
      try {
        localStorage.setItem('calorieTrackerState', JSON.stringify(appState));
      } catch (error) {
        console.error("Could not save state to localStorage", error);
      }
    }
  }, [appState, isInitialized]);

  const updateState = useCallback((updater: (prevState: AppState) => AppState) => {
    setAppState(updater);
  }, []);

  const getLogForDate = useCallback((date: string): DailyLog => {
    return appState.logs[date] || { food: [], exercise: [] };
  }, [appState.logs]);

  const addFood = useCallback((food: Omit<FoodEntry, 'id'>) => {
    updateState(prev => {
      const newFoodEntry: FoodEntry = { ...food, id: Date.now().toString() };
      const currentLog = prev.logs[dateString] || { food: [], exercise: [] };
      const newLog = { ...currentLog, food: [...currentLog.food, newFoodEntry] };
      return { ...prev, logs: { ...prev.logs, [dateString]: newLog } };
    });
  }, [updateState, dateString]);

  const addExercise = useCallback((exercise: Omit<ExerciseEntry, 'id'>) => {
    updateState(prev => {
      const newExerciseEntry: ExerciseEntry = { ...exercise, id: Date.now().toString() };
      const currentLog = prev.logs[dateString] || { food: [], exercise: [] };
      const newLog = { ...currentLog, exercise: [...currentLog.exercise, newExerciseEntry] };
      return { ...prev, logs: { ...prev.logs, [dateString]: newLog } };
    });
  }, [updateState, dateString]);
  
  const removeFood = useCallback((foodId: string) => {
    updateState(prev => {
        const currentLog = prev.logs[dateString];
        if (!currentLog) return prev;
        const newFood = currentLog.food.filter(f => f.id !== foodId);
        const newLog = { ...currentLog, food: newFood };
        return { ...prev, logs: { ...prev.logs, [dateString]: newLog } };
    });
  }, [updateState, dateString]);

  const removeExercise = useCallback((exerciseId: string) => {
      updateState(prev => {
          const currentLog = prev.logs[dateString];
          if (!currentLog) return prev;
          const newExercise = currentLog.exercise.filter(e => e.id !== exerciseId);
          const newLog = { ...currentLog, exercise: newExercise };
          return { ...prev, logs: { ...prev.logs, [dateString]: newLog } };
      });
  }, [updateState, dateString]);

  const setDailyGoal = useCallback((goal: number) => {
    updateState(prev => ({...prev, dailyGoal: goal}));
  }, [updateState]);

  const setApiKey = useCallback((apiKey: string | null) => {
    updateState(prev => ({ ...prev, apiKey }));
  }, [updateState]);

  const setAiModel = useCallback((aiModel: string) => {
    updateState(prev => ({ ...prev, aiModel }));
  }, [updateState]);

  const setLanguage = useCallback((language: 'en' | 'zh-TW') => {
    updateState(prev => ({ ...prev, language }));
  }, [updateState]);

  const updateUserProfile = useCallback((profile: Partial<UserProfile>) => {
    updateState(prev => ({ ...prev, userProfile: { ...prev.userProfile, ...profile }}));
  }, [updateState]);

  const setChatHistory = useCallback((history: ChatMessage[]) => {
    updateState(prev => ({...prev, chatHistory: history}));
  }, [updateState]);

  const importData = useCallback((newState: AppState) => {
    if (newState && newState.logs && typeof newState.dailyGoal === 'number') {
        setAppState(newState);
        return true;
    }
    return false;
  }, [setAppState]);

  return { 
    appState, 
    updateState,
    getLogForDate, 
    addFood, 
    addExercise, 
    removeFood, 
    removeExercise, 
    setDailyGoal, 
    setApiKey, 
    setAiModel, 
    isInitialized,
    selectedDate,
    setSelectedDate,
    setLanguage,
    updateUserProfile,
    setChatHistory,
    importData,
  };
};